# Leakin Bot - Multi-Server Licensing System

A Discord bot with multi-server licensing that assigns roles based on user status containing a trigger word. Each server requires a license key to operate.

## Features

### License Management
- **License Key Redemption**: Server owners can redeem license keys for their servers
- **Multi-Server Support**: Each license key can be used on one server at a time
- **Key Transfer**: License owners can transfer keys between servers or users
- **Admin Key Management**: Authorized admins can add new license keys to the system

### Server Configuration
- **Role Assignment**: Configure which role is assigned when users have the trigger word in their status
- **Channel Setup**: Set notification and logging channels
- **User Ignoring**: Add users (like bots) to an ignore list
- **Status Monitoring**: Automatic role assignment/removal based on user status

## Setup Instructions

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Bot Settings**
   Edit `main.py` and update the configuration section:
   ```python
   BOT_TOKEN = "your_discord_bot_token_here"
   MONGO_URL = "your_mongodb_connection_string"
   ADMIN_USER_ID = your_discord_user_id_for_admin_commands
   TRIGGER_WORD = "/leakin"  # Word to look for in user status
   ```

3. **Database Setup**
   - The bot uses MongoDB with collections following the format `leakin-tablename`
   - Collections created automatically:
     - `leakin-license-keys`: Stores license key information
     - `leakin-server-configs`: Stores server configuration

4. **Run the Bot**
   ```bash
   python main.py
   ```

## Commands

### License Management Commands

#### `/redeem-key <license-key>`
- **Description**: Redeem a license key for the current server
- **Permission**: Server owner only
- **Usage**: `/redeem-key ABC123DEF456`

#### `/add-keys <file>`
- **Description**: Add license keys from a text file
- **Permission**: Admin only (user ID specified in config)
- **File Format**: Text file with space or newline separated license keys
- **Usage**: Upload a .txt file with the command

#### `/transfer-key <license-key> <server-id>`
- **Description**: Transfer your license key to a different server
- **Permission**: License key owner only
- **Usage**: `/transfer-key ABC123DEF456 *********0*********`

#### `/transfer-key-user <license-key> <user-id>`
- **Description**: Transfer ownership of your license key to another user
- **Permission**: License key owner only
- **Usage**: `/transfer-key-user ABC123DEF456 *********0*********`

### Server Configuration Commands
*All require a valid license and server owner permissions*

#### `/rep <trigger_word> <role>`
- **Description**: Set up server repping system with trigger word and role
- **Usage**: `/rep /leakin @LeakinRole`
- **Restrictions:** Server owner only, requires valid license
- **Parameters:**
  - Trigger word (1-50 characters) - The word to look for in user statuses
  - Role - The role to assign to users with the trigger word in their status
- **Note:** The bot will search for this word anywhere in the user's custom status (substring match)

#### `/set-channel-id <channel>`
- **Description**: Set the channel for bot notifications
- **Usage**: `/set-channel-id #notifications`

#### `/set-log-id <channel>`
- **Description**: Set the channel for detailed logging
- **Usage**: `/set-log-id #bot-logs`

#### `/add-ignored-user <user>`
- **Description**: Add a user to the ignored list
- **Usage**: `/add-ignored-user @SomeBot`

### Information Commands

#### `/server-status`
- **Description**: Check server configuration and license status
- **Permission**: Requires valid license
- **Usage**: `/server-status`

#### `/my-keys`
- **Description**: View your license keys
- **Usage**: `/my-keys`

#### `/system-status`
- **Description**: Check system-wide status
- **Permission**: Admin only
- **Usage**: `/system-status`

## How It Works

1. **License Verification**: All commands except `/redeem-key` and `/add-keys` require a valid license
2. **Status Monitoring**: Bot checks user statuses every minute across all licensed servers
3. **Role Assignment**: When a user's status contains the trigger word, they receive the configured role
4. **Role Removal**: When the trigger word is removed or user goes offline, the role is removed
5. **Multi-Server**: Each server operates independently with its own configuration

## Database Collections

### `leakin-license-keys`
```json
{
  "key": "license_key_string",
  "redeemed": true/false,
  "redeemed_by": user_id,
  "redeemed_at": timestamp,
  "server_id": server_id,
  "created_at": timestamp
}
```

### `leakin-server-configs`
```json
{
  "server_id": *********,
  "role_id": *********,
  "channel_id": *********,
  "log_channel_id": *********,
  "trigger_word": "/leakin",
  "ignored_users": [*********, *********],
  "updated_at": timestamp
}
```

## Requirements

- Python 3.8+
- discord.py 2.3.2+
- pymongo 4.6.0+
- MongoDB Atlas account or MongoDB server

## Security Notes

- License keys are stored securely in MongoDB
- Only server owners can redeem keys and configure servers
- Admin commands are restricted to specified user ID
- All license operations are logged

## Support

For issues or questions, please check the configuration and ensure:
1. Bot has proper permissions in Discord servers
2. MongoDB connection is working
3. License keys are valid and not already redeemed
4. Server configuration is complete

## License

This bot is designed for authorized use only. Each server requires a valid license key to operate.

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON> – Multi-Purpose Discord <PERSON></title>
  <style>
    :root {
      --bg: #0d1117;
      --card: #161b22;
      --accent: #238636;
      --accent-hover: #2ea043;
      --border: #30363d;
      --text: #e6edf3;
      --subtext: #8b949e;
      --link: #58a6ff;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    body {
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 40px 20px;
    }

    .container {
      width: 100%;
      max-width: 900px;
    }

    .header {
      text-align: center;
      margin-bottom: 50px;
    }

    .header h1 {
      font-size: 3em;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--text);
    }

    .header p {
      font-size: 1.1em;
      color: var(--subtext);
    }

    .section {
      background: var(--card);
      border: 1px solid var(--border);
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 40px;
    }

    .section h2 {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--text);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }

    .section p,
    .section li {
      color: #c9d1d9;
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: 500;
      text-decoration: none;
      transition: background 0.2s ease, transform 0.2s ease;
      margin: 10px 8px 0 0;
    }

    .btn-primary {
      background: var(--accent);
      color: white;
    }

    .btn-primary:hover {
      background: var(--accent-hover);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: transparent;
      color: var(--link);
      border: 1px solid var(--border);
    }

    .btn-secondary:hover {
      background: #21262d;
      border-color: var(--link);
    }

    .feature-list ul {
      list-style: none;
      padding-left: 0;
    }

    .feature-list li::before {
      content: "•";
      color: var(--link);
      margin-right: 8px;
    }

    .command-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .command-card {
      background: #21262d;
      border: 1px solid var(--border);
      border-radius: 6px;
      padding: 20px;
    }

    .command-card .command-name {
      font-weight: bold;
      color: var(--text);
      font-size: 1.1em;
      margin-bottom: 8px;
    }

    code {
      background: #30363d;
      padding: 3px 6px;
      border-radius: 4px;
      font-family: Consolas, monospace;
      font-size: 0.95em;
    }

    .footer {
      text-align: center;
      color: var(--subtext);
      margin-top: 50px;
      font-size: 0.95em;
    }

    .footer a {
      color: var(--link);
    }

    @media (max-width: 600px) {
      .header h1 {
        font-size: 2em;
      }

      .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Leakin Bot</h1>
      <p>Auto role assignment based on user status</p>
    </div>

    <div class="section">
      <h2>Get Started</h2>
      <p>Purchase a license and add Leakin Bot to your Discord server.</p>
      <a href="https://leakin.cc" target="_blank" class="btn btn-secondary">Purchase License</a>
      <a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank" class="btn btn-primary">Add to Server</a>
    </div>

    <div class="section">
      <h2>Features</h2>
      <div class="feature-list">
        <ul>
          <li>Status-based auto role assignment</li>
          <li>Fast updates (checks every 10s)</li>
          <li>Multi-server configuration</li>
          <li>Rate limit safe</li>
          <li>Logging of role changes</li>
          <li>License required per server</li>
        </ul>
      </div>
    </div>

    <div class="section">
      <h2>Setup Steps</h2>
      <ol>
        <li>Buy your license key at <a href="https://leakin.cc" target="_blank">leakin.cc</a></li>
        <li>Add the bot to your server</li>
        <li>Redeem your key: <code>/redeem-key your-license</code></li>
        <li>Set role: <code>/set-role-id @Role</code></li>
        <li>Set ignored users: <code>/add-ignored-user @User</code></li>
        <li>Set channel: <code>/set-channel-id #channel</code></li>
        <li>Set log channel: <code>/set-log-id #channel</code></li>
        <li>Set trigger: <code>/set-trigger-word /yourword</code></li>
        <li>Done!</li>
      </ol>
    </div>

    <div class="section">
      <h2>Commands</h2>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/redeem-key</div>
          <p>Redeem license for your server</p>
          <code>/redeem-key ABC123XYZ</code>
        </div>
        <div class="command-card">
          <div class="command-name">/set-role-id</div>
          <p>Set the role to assign</p>
          <code>/set-role-id @VIP</code>
        </div>
        <div class="command-card">
          <div class="command-name">/set-trigger-word</div>
          <p>Configure your trigger word</p>
          <code>/set-trigger-word /leakin</code>
        </div>
        <div class="command-card">
          <div class="command-name">/server-status</div>
          <p>View your server config</p>
          <code>/server-status</code>
        </div>
        <div class="command-card">
            <div class="command-name">/add-ignored-user</div>
            <p>Add a user to the ignored list.</p>
            <code>/add-ignored-user</code>
        </div>
        <div class="command-card">
            <div class="command-name">/my-keys</div>
            <p>View your license keys.</p>
            <code>/my-keys</code>
        </div>
        <div class="command-card">
            <div class="command-name">/set-channel-id</div>
            <p>Set the channel to send notifications to.</p>
            <code>/set-channel-id #channel</code>
        </div>
        <div class="command-card">
            <div class="command-name">/set-log-id</div>
            <p>Set the channel to send logs to.</p>
            <code>/set-log-id #channel</code>
        </div>
        <div class="command-card">
            <div class="command-name">/transfer-key</div>
            <p>Transfer your license key to another server.</p>
            <code>/transfer-key</code>
        </div>
        <div class="command-card">
            <div class="command-name">/transfer-key-user</div>
            <p>Transfer your license key to another user.</p>
            <code>/transfer-key-user</code>
        </div>
      </div>
    </div>

    <div class="footer">
      &copy; 2025 Leakin Bot – <a href="https://leakin.cc" target="_blank">leakin.cc</a>
    </div>
  </div>
</body>
</html>
